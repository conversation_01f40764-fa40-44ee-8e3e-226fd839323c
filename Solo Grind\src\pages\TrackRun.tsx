
import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { GlassCard } from "@/components/GlassCard";
import { BottomNav } from "@/components/BottomNav";
import { toast } from "sonner";
import { Loader2, Play, Square, Timer, Footprints } from "lucide-react";
import { getDistanceFromLatLonInKm } from "@/lib/geo";

type GeolocationCoords = {
  latitude: number;
  longitude: number;
};

export default function TrackRun() {
  const { user } = useAuth();
  const [isTracking, setIsTracking] = useState(false);
  const [distance, setDistance] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const watchId = useRef<number | null>(null);
  const lastPosition = useRef<GeolocationCoords | null>(null);
  const timerInterval = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    // Clear watch and interval on component unmount
    return () => {
      if (watchId.current) navigator.geolocation.clearWatch(watchId.current);
      if (timerInterval.current) clearInterval(timerInterval.current);
    };
  }, []);

  const handleStart = () => {
    if (!navigator.geolocation) {
      setError("Geolocation is not supported by your browser.");
      return;
    }

    // Reset state for a new run
    setDistance(0);
    setDuration(0);
    setError(null);
    lastPosition.current = null;
    setIsTracking(true);

    timerInterval.current = setInterval(() => {
      setDuration(d => d + 1);
    }, 1000);

    watchId.current = navigator.geolocation.watchPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        if (lastPosition.current) {
          const newDistance = getDistanceFromLatLonInKm(
            lastPosition.current.latitude,
            lastPosition.current.longitude,
            latitude,
            longitude
          );
          setDistance((d) => d + newDistance);
        }
        lastPosition.current = { latitude, longitude };
      },
      (err) => {
        setError(`Error getting location: ${err.message}`);
        handleStop(false); // Stop tracking on error
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  };

  const handleStop = async (shouldSave = true) => {
    if (watchId.current) navigator.geolocation.clearWatch(watchId.current);
    if (timerInterval.current) clearInterval(timerInterval.current);
    
    setIsTracking(false);

    if (shouldSave && distance > 0 && user) {
      setIsSaving(true);
      const { error: insertError } = await supabase.from("user_activities").insert({
        user_id: user.id,
        distance_km: parseFloat(distance.toFixed(2)),
        activity_type: "Run",
        activity_date: new Date().toISOString().split("T")[0],
      });
      setIsSaving(false);
      
      if (insertError) {
        toast.error("Failed to save run.", { description: insertError.message });
      } else {
        toast.success("Run saved successfully!", {
          description: `You ran ${distance.toFixed(2)} km.`,
        });
        // Reset for next run
        setDistance(0);
        setDuration(0);
      }
    }
  };
  
  const formatDuration = (seconds: number) => {
    const h = Math.floor(seconds / 3600).toString().padStart(2, '0');
    const m = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
    const s = (seconds % 60).toString().padStart(2, '0');
    return `${h}:${m}:${s}`;
  };

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70 flex flex-col items-center justify-center">
        <h1 className="text-4xl font-bold gradient-title mb-8">Track Your Run</h1>
        
        <div className="w-full max-w-sm px-4 space-y-4">
            <GlassCard className="p-6 flex flex-col items-center">
                <Footprints className="text-electric mb-2" size={32} />
                <span className="text-sm text-white/70">DISTANCE</span>
                <span className="text-4xl font-bold text-white">{distance.toFixed(2)} km</span>
            </GlassCard>

            <GlassCard className="p-6 flex flex-col items-center">
                <Timer className="text-electric mb-2" size={32} />
                <span className="text-sm text-white/70">DURATION</span>
                <span className="text-4xl font-bold text-white">{formatDuration(duration)}</span>
            </GlassCard>
        </div>
        
        <div className="mt-8">
            {isTracking ? (
                <Button onClick={() => handleStop()} size="lg" className="bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-10 rounded-full text-xl shadow-glow">
                    <Square className="mr-2" size={24} /> Stop
                </Button>
            ) : (
                <Button onClick={handleStart} size="lg" className="bg-electric hover:bg-purple text-white font-bold py-4 px-10 rounded-full text-xl shadow-glow" disabled={isSaving}>
                    {isSaving ? (
                        <>
                            <Loader2 className="mr-2 h-6 w-6 animate-spin" /> Saving...
                        </>
                    ) : (
                        <>
                            <Play className="mr-2" size={24} /> Start
                        </>
                    )}
                </Button>
            )}
        </div>

        {error && (
            <p className="text-red-500 mt-4 text-center">{error}</p>
        )}
        
        <BottomNav />
      </div>
    </>
  );
}
