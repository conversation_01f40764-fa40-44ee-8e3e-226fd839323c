
import { Link, useLocation } from "react-router-dom";
import { <PERSON>, User, <PERSON><PERSON><PERSON>, Grid2x2, ShieldCheck, MessageSquare, Footprints } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const navs = [
  { icon: <Home size={24} />, label: "Home", to: "/" },
  { icon: <Footprints size={24} />, label: "Track", to: "/track-run" },
  { icon: <MessageSquare size={24} />, label: "Chat", to: "/chat" },
  { icon: <BarChart size={24} />, label: "Rank", to: "/leaderboard" },
  { icon: <Grid2x2 size={24} />, label: "Guild", to: "/guild" },
];

export function BottomNav() {
  const { pathname } = useLocation();
  const { role } = useAuth();

  const allNavs = [...navs];
  if (role === 'admin') {
    // Add admin link to the end
    allNavs.splice(navs.length, 0, { icon: <ShieldCheck size={24} />, label: "Admin", to: "/admin" });
  }

  return (
    <nav className="fixed z-50 bottom-0 left-0 right-0 h-16 bg-glass backdrop-blur-xl border-t border-white/10 flex items-center shadow-glass overflow-x-auto px-4 gap-4">
      {allNavs.map((item) => {
        const active = pathname === item.to;
        return (
          <Link
            key={item.to}
            to={item.to}
            className={`flex flex-col items-center gap-0.5 px-2 group shrink-0 ${active ? "text-electric" : "text-white/80"}`}
            style={active ? { filter: "drop-shadow(0 0 4px #00d4ff)" } : {}}
          >
            {item.icon}
            <span className="text-2xs text-[.75rem]">{item.label}</span>
          </Link>
        );
      })}
    </nav>
  );
}
