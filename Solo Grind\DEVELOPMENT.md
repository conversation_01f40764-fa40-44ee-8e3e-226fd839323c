# SoloGrind Development Guide

## Quick Start

### Windows Users
1. **Start Development Server**: Double-click `start-dev.bat`
2. **Start with Browser**: Double-click `start-browser.bat` (automatically opens browser)

### Mac/Linux Users
1. **Make scripts executable** (first time only):
   ```bash
   chmod +x start-dev.sh start-browser.sh
   ```
2. **Start Development Server**: `./start-dev.sh`
3. **Start with Browser**: `./start-browser.sh` (automatically opens browser)

### Manual Start
```bash
npm install          # Install dependencies (first time only)
npm run dev         # Start development server
```

Then open http://localhost:8080 in your browser.

## Environment Configurations

### Development Mode (Default)
- **Command**: `npm run dev`
- **Environment**: `.env.development`
- **Features**: 
  - Subscriptions: Disabled
  - Analytics: Disabled
  - Payments: Disabled
  - Debug Mode: Enabled

### Test Mode
- **Command**: `npm run dev:test`
- **Environment**: `.env.test`
- **Features**: 
  - All features disabled for testing
  - Mock data enabled

### Production Mode (Local)
- **Command**: `npm run dev:prod`
- **Environment**: `.env.production`
- **Features**: 
  - All features enabled
  - Analytics enabled
  - Payments enabled

## Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server (development mode) |
| `npm run dev:test` | Start development server (test mode) |
| `npm run dev:prod` | Start development server (production mode) |
| `npm run build` | Build for production |
| `npm run build:dev` | Build for development |
| `npm run build:test` | Build for testing |
| `npm run build:prod` | Build for production |
| `npm run preview` | Preview production build |
| `npm run lint` | Run ESLint |

## Environment Variables

### Development (.env.development)
- `VITE_ENABLE_SUBSCRIPTIONS=false` - Disables subscription features
- `VITE_ENABLE_ANALYTICS=false` - Disables analytics tracking
- `VITE_ENABLE_PAYMENTS=false` - Disables payment processing
- `VITE_DEBUG_MODE=true` - Enables debug logging

### Production (.env.production)
- `VITE_ENABLE_SUBSCRIPTIONS=true` - Enables subscription features
- `VITE_ENABLE_ANALYTICS=true` - Enables analytics tracking
- `VITE_ENABLE_PAYMENTS=true` - Enables payment processing
- `VITE_DEBUG_MODE=false` - Disables debug logging

## Database Setup

The application uses Supabase as the backend. The database is already configured and migrations are in the `supabase/migrations` folder.

### Key Tables:
- `profiles` - User profiles
- `user_activities` - Fitness tracking data
- `guilds` - User groups/communities
- `wallets` - User wallet balances
- `messages` - Chat messages

## Development Features

### Disabled in Development:
1. **Subscription System** - No payment processing
2. **Analytics Tracking** - No data collection
3. **Email Notifications** - No email sending

### Available in Development:
1. **Authentication** - Full Supabase auth
2. **Database Operations** - Full CRUD operations
3. **Real-time Features** - Chat, leaderboards
4. **File Uploads** - Avatar uploads

## Troubleshooting

### Common Issues:
1. **Port 8080 already in use**: Change port in `vite.config.ts`
2. **Dependencies not installed**: Run `npm install`
3. **Environment variables not loading**: Check `.env.*` files exist

### Reset Development Environment:
```bash
rm -rf node_modules package-lock.json
npm install
npm run dev
```

## Mobile Development

The app is configured for mobile deployment using Capacitor:

```bash
npm run build
npx cap add android
npx cap run android
```

## Next Steps

1. Configure analytics for production
2. Set up Stripe for payments
3. Configure email notifications
4. Set up CI/CD pipeline
