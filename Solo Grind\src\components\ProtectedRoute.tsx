
import { useAuth } from "@/contexts/AuthContext";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { Loader2 } from "lucide-react";

type ProtectedRouteProps = {
  adminOnly?: boolean;
};

export function ProtectedRoute({ adminOnly = false }: ProtectedRouteProps) {
  const { user, loading, role, isTrialActive, isSubscribed } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="min-h-screen bg-backdrop flex items-center justify-center">
        <Loader2 className="h-8 w-8 text-electric animate-spin" />
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // Allow access to subscription page for users who need to subscribe.
  if (location.pathname === "/subscription") {
    return <Outlet />;
  }

  // If trial is over and user is not subscribed, redirect to subscription page.
  // This check does not apply to admins.
  if (!isTrialActive && !isSubscribed && role !== 'admin') {
    return <Navigate to="/subscription" replace />;
  }

  if (adminOnly && role !== "admin") {
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
}
