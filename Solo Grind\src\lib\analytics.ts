// Analytics and tracking system
import { isAnalyticsEnabled, debugLog, config } from './config';

// Event types for tracking
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  userId?: string;
  timestamp?: Date;
}

// User properties for identification
export interface UserProperties {
  userId: string;
  username?: string;
  email?: string;
  tier?: string;
  subscriptionStatus?: string;
  joinDate?: Date;
}

class AnalyticsService {
  private isInitialized = false;
  private eventQueue: AnalyticsEvent[] = [];

  constructor() {
    this.initialize();
  }

  private initialize() {
    if (!isAnalyticsEnabled()) {
      debugLog('Analytics disabled in current environment');
      return;
    }

    // Initialize Google Analytics
    if (config.analytics.googleAnalyticsId) {
      this.initializeGoogleAnalytics();
    }

    // Initialize Mixpanel
    if (config.analytics.mixpanelToken) {
      this.initializeMixpanel();
    }

    this.isInitialized = true;
    debugLog('Analytics service initialized');

    // Process queued events
    this.processEventQueue();
  }

  private initializeGoogleAnalytics() {
    try {
      // Load Google Analytics script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${config.analytics.googleAnalyticsId}`;
      document.head.appendChild(script);

      // Initialize gtag
      (window as any).dataLayer = (window as any).dataLayer || [];
      function gtag(...args: any[]) {
        (window as any).dataLayer.push(args);
      }
      (window as any).gtag = gtag;

      gtag('js', new Date());
      gtag('config', config.analytics.googleAnalyticsId);

      debugLog('Google Analytics initialized');
    } catch (error) {
      console.error('Failed to initialize Google Analytics:', error);
    }
  }

  private initializeMixpanel() {
    try {
      // Load Mixpanel script (simplified version)
      debugLog('Mixpanel would be initialized here with token:', config.analytics.mixpanelToken);
      // In a real implementation, you would load the Mixpanel SDK
    } catch (error) {
      console.error('Failed to initialize Mixpanel:', error);
    }
  }

  private processEventQueue() {
    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift();
      if (event) {
        this.sendEvent(event);
      }
    }
  }

  // Track an event
  track(eventName: string, properties?: Record<string, any>, userId?: string) {
    const event: AnalyticsEvent = {
      name: eventName,
      properties: {
        ...properties,
        environment: config.app.environment,
        timestamp: new Date().toISOString(),
      },
      userId,
      timestamp: new Date(),
    };

    if (!isAnalyticsEnabled()) {
      debugLog('Analytics Event (not sent):', event);
      return;
    }

    if (!this.isInitialized) {
      this.eventQueue.push(event);
      return;
    }

    this.sendEvent(event);
  }

  private sendEvent(event: AnalyticsEvent) {
    debugLog('Sending analytics event:', event);

    // Send to Google Analytics
    if (config.analytics.googleAnalyticsId && (window as any).gtag) {
      (window as any).gtag('event', event.name, {
        ...event.properties,
        user_id: event.userId,
      });
    }

    // Send to Mixpanel
    if (config.analytics.mixpanelToken) {
      // In a real implementation, you would use Mixpanel SDK
      debugLog('Would send to Mixpanel:', event);
    }

    // Send to custom backend (optional)
    this.sendToBackend(event);
  }

  private async sendToBackend(event: AnalyticsEvent) {
    try {
      // You could send events to your own backend for custom analytics
      debugLog('Would send to backend:', event);
      
      // Example implementation:
      // await fetch('/api/analytics', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event),
      // });
    } catch (error) {
      console.error('Failed to send event to backend:', error);
    }
  }

  // Identify a user
  identify(userProperties: UserProperties) {
    if (!isAnalyticsEnabled()) {
      debugLog('User identification (not sent):', userProperties);
      return;
    }

    debugLog('Identifying user:', userProperties);

    // Google Analytics user identification
    if ((window as any).gtag) {
      (window as any).gtag('config', config.analytics.googleAnalyticsId, {
        user_id: userProperties.userId,
      });
    }

    // Mixpanel user identification
    if (config.analytics.mixpanelToken) {
      debugLog('Would identify user in Mixpanel:', userProperties);
    }
  }

  // Page view tracking
  pageView(pageName: string, additionalProperties?: Record<string, any>) {
    this.track('page_view', {
      page_name: pageName,
      page_url: window.location.href,
      page_path: window.location.pathname,
      ...additionalProperties,
    });
  }

  // User action tracking
  userAction(action: string, properties?: Record<string, any>) {
    this.track('user_action', {
      action,
      ...properties,
    });
  }

  // Performance tracking
  performance(metric: string, value: number, properties?: Record<string, any>) {
    this.track('performance', {
      metric,
      value,
      ...properties,
    });
  }

  // Error tracking
  error(error: Error | string, properties?: Record<string, any>) {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'object' ? error.stack : undefined;

    this.track('error', {
      error_message: errorMessage,
      error_stack: errorStack,
      ...properties,
    });
  }
}

// Create singleton instance
export const analytics = new AnalyticsService();

// Convenience functions
export const trackEvent = (name: string, properties?: Record<string, any>, userId?: string) => {
  analytics.track(name, properties, userId);
};

export const trackPageView = (pageName: string, properties?: Record<string, any>) => {
  analytics.pageView(pageName, properties);
};

export const trackUserAction = (action: string, properties?: Record<string, any>) => {
  analytics.userAction(action, properties);
};

export const identifyUser = (userProperties: UserProperties) => {
  analytics.identify(userProperties);
};

export const trackError = (error: Error | string, properties?: Record<string, any>) => {
  analytics.error(error, properties);
};

export const trackPerformance = (metric: string, value: number, properties?: Record<string, any>) => {
  analytics.performance(metric, value, properties);
};

// Fitness-specific tracking events
export const trackWorkout = (workoutData: {
  type: string;
  distance?: number;
  duration?: number;
  calories?: number;
}) => {
  trackEvent('workout_completed', workoutData);
};

export const trackAchievement = (achievementData: {
  achievementId: string;
  achievementName: string;
  tier?: string;
}) => {
  trackEvent('achievement_unlocked', achievementData);
};

export const trackGuildAction = (action: string, guildData?: {
  guildId?: string;
  guildName?: string;
}) => {
  trackEvent('guild_action', { action, ...guildData });
};

export const trackSubscription = (action: string, subscriptionData?: {
  plan?: string;
  amount?: number;
}) => {
  trackEvent('subscription_action', { action, ...subscriptionData });
};
