
-- Create a table to store user activities
CREATE TABLE public.user_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    distance_km NUMERIC(5, 2) NOT NULL,
    activity_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT fk_profile
      FOREIGN KEY(user_id) 
	  REFERENCES public.profiles(id)
	  ON DELETE CASCADE
);

-- Add comments for clarity
COMMENT ON TABLE public.user_activities IS 'Stores daily distance covered by users.';
COMMENT ON COLUMN public.user_activities.distance_km IS 'Distance covered in kilometers.';

-- Enable Row-Level Security
ALTER TABLE public.user_activities ENABLE ROW LEVEL SECURITY;

-- Policies for access control
CREATE POLICY "Admins can view all user activities"
ON public.user_activities FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'
  )
);

CREATE POLICY "Users can view their own activities"
ON public.user_activities FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activities"
ON public.user_activities FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own activities"
ON public.user_activities FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own activities"
ON public.user_activities FOR DELETE
TO authenticated
USING (auth.uid() = user_id);
