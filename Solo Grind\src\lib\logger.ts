// Logging system for the application
import { isDebugMode, config } from './config';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: Date;
  source?: string;
  userId?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory

  constructor() {
    // Set up global error handler
    this.setupGlobalErrorHandler();
  }

  private setupGlobalErrorHandler() {
    // Catch unhandled errors
    window.addEventListener('error', (event) => {
      this.error('Unhandled Error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
      });
    });

    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled Promise Rejection', {
        reason: event.reason,
      });
    });
  }

  private addLog(level: LogLevel, message: string, data?: any, source?: string) {
    const logEntry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date(),
      source,
    };

    // Add to memory logs
    this.logs.push(logEntry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output based on environment
    this.outputToConsole(logEntry);

    // Send to backend in production
    if (config.app.environment === 'production' && level >= LogLevel.WARN) {
      this.sendToBackend(logEntry);
    }
  }

  private outputToConsole(logEntry: LogEntry) {
    const { level, message, data, timestamp, source } = logEntry;
    const timeStr = timestamp.toISOString();
    const sourceStr = source ? `[${source}]` : '';
    const fullMessage = `${timeStr} ${sourceStr} ${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        if (isDebugMode()) {
          console.debug(fullMessage, data);
        }
        break;
      case LogLevel.INFO:
        console.info(fullMessage, data);
        break;
      case LogLevel.WARN:
        console.warn(fullMessage, data);
        break;
      case LogLevel.ERROR:
        console.error(fullMessage, data);
        break;
    }
  }

  private async sendToBackend(logEntry: LogEntry) {
    try {
      // In a real implementation, you would send logs to your backend
      // await fetch('/api/logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(logEntry),
      // });
      
      if (isDebugMode()) {
        console.debug('Would send log to backend:', logEntry);
      }
    } catch (error) {
      console.error('Failed to send log to backend:', error);
    }
  }

  // Public logging methods
  debug(message: string, data?: any, source?: string) {
    this.addLog(LogLevel.DEBUG, message, data, source);
  }

  info(message: string, data?: any, source?: string) {
    this.addLog(LogLevel.INFO, message, data, source);
  }

  warn(message: string, data?: any, source?: string) {
    this.addLog(LogLevel.WARN, message, data, source);
  }

  error(message: string, data?: any, source?: string) {
    this.addLog(LogLevel.ERROR, message, data, source);
  }

  // Get logs for debugging
  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level);
    }
    return [...this.logs];
  }

  // Clear logs
  clearLogs() {
    this.logs = [];
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Performance logging
  time(label: string) {
    console.time(label);
  }

  timeEnd(label: string) {
    console.timeEnd(label);
    this.debug(`Performance: ${label} completed`);
  }

  // Group logging
  group(label: string) {
    console.group(label);
    this.debug(`Group started: ${label}`);
  }

  groupEnd() {
    console.groupEnd();
    this.debug('Group ended');
  }
}

// Create singleton instance
export const logger = new Logger();

// Convenience functions
export const logDebug = (message: string, data?: any, source?: string) => {
  logger.debug(message, data, source);
};

export const logInfo = (message: string, data?: any, source?: string) => {
  logger.info(message, data, source);
};

export const logWarn = (message: string, data?: any, source?: string) => {
  logger.warn(message, data, source);
};

export const logError = (message: string, data?: any, source?: string) => {
  logger.error(message, data, source);
};

// Application-specific logging functions
export const logUserAction = (action: string, data?: any) => {
  logger.info(`User Action: ${action}`, data, 'USER_ACTION');
};

export const logDatabaseOperation = (operation: string, table: string, data?: any) => {
  logger.debug(`Database: ${operation} on ${table}`, data, 'DATABASE');
};

export const logAuthEvent = (event: string, data?: any) => {
  logger.info(`Auth: ${event}`, data, 'AUTH');
};

export const logApiCall = (endpoint: string, method: string, data?: any) => {
  logger.debug(`API: ${method} ${endpoint}`, data, 'API');
};

export const logPerformance = (metric: string, value: number, data?: any) => {
  logger.info(`Performance: ${metric} = ${value}ms`, data, 'PERFORMANCE');
};

export const logWorkout = (workoutType: string, data?: any) => {
  logger.info(`Workout: ${workoutType}`, data, 'WORKOUT');
};

export const logGuildActivity = (activity: string, data?: any) => {
  logger.info(`Guild: ${activity}`, data, 'GUILD');
};

export const logSubscriptionEvent = (event: string, data?: any) => {
  logger.info(`Subscription: ${event}`, data, 'SUBSCRIPTION');
};

// Error boundary logging
export const logReactError = (error: Error, errorInfo: any) => {
  logger.error('React Error Boundary', {
    error: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
  }, 'REACT');
};

// Network error logging
export const logNetworkError = (url: string, error: any) => {
  logger.error('Network Error', {
    url,
    error: error.message || error,
  }, 'NETWORK');
};

// Validation error logging
export const logValidationError = (field: string, value: any, error: string) => {
  logger.warn('Validation Error', {
    field,
    value,
    error,
  }, 'VALIDATION');
};
