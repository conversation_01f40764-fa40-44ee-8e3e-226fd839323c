
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { GlassCard } from "../GlassCard";
import { format } from "date-fns";
import { Tables } from "@/integrations/supabase/types";
import { cn } from "@/lib/utils";

interface Props {
  transactions: Tables<'wallet_transactions'>[];
}

export function TransactionHistory({ transactions }: Props) {
  return (
    <GlassCard className="p-4">
      <h2 className="text-xl mb-4 text-white/90">Transaction History</h2>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-white/80">Date</TableHead>
              <TableHead className="text-white/80">Description</TableHead>
              <TableHead className="text-right text-white/80">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="text-center text-white/70 h-24">
                  No transactions yet.
                </TableCell>
              </TableRow>
            ) : (
              transactions.map((tx) => (
                <TableRow key={tx.id}>
                  <TableCell className="text-white/90 text-xs">
                    {format(new Date(tx.created_at), "MMM d, yyyy")}
                  </TableCell>
                  <TableCell className="font-medium text-white/90">{tx.description}</TableCell>
                  <TableCell
                    className={cn(
                      "text-right font-mono",
                      tx.amount > 0 ? "text-green-400" : "text-red-400"
                    )}
                  >
                    {tx.amount > 0 ? "+" : ""}${Math.abs(tx.amount).toFixed(2)}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </GlassCard>
  );
}
