
-- A view for individual user leaderboards, aggregating total distance.
CREATE OR REPLACE VIEW public.user_leaderboard_stats AS
SELECT
  p.id AS user_id,
  p.username,
  p.avatar_url,
  COALESCE(SUM(ua.distance_km), 0) AS total_distance
FROM
  public.profiles p
LEFT JOIN
  public.user_activities ua ON p.id = ua.user_id
GROUP BY
  p.id, p.username, p.avatar_url;

-- Table to store guilds
CREATE TABLE public.guilds (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  icon_name TEXT, -- Corresponds to lucide-react icon names
  xp INT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Table to link users to guilds
CREATE TABLE public.guild_members (
  guild_id uuid NOT NULL REFERENCES public.guilds(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member', -- e.g., 'member', 'leader'
  joined_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  PRIMARY KEY (guild_id, user_id)
);

-- Enable Row Level Security for new tables
ALTER TABLE public.guilds ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guild_members ENABLE ROW LEVEL SECURITY;

-- RLS Policies (making them publicly viewable for the leaderboard)
CREATE POLICY "Guilds are viewable by everyone." ON public.guilds FOR SELECT USING (true);
CREATE POLICY "Guild members are viewable by everyone." ON public.guild_members FOR SELECT USING (true);

-- Seed some initial guild data to populate the leaderboard page
INSERT INTO public.guilds (name, icon_name, xp) VALUES
('Neon Runners', 'Zap', 125000),
('Cyber Striders', 'Cpu', 110000),
('Synthwave Sprinters', 'Waves', 98000),
('Data Dashers', 'Database', 85000),
('Quantum Leapers', 'Atom', 72000);
