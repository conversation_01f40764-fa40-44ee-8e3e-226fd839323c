
import { CreateGuildDialog } from "@/components/guild/CreateGuildDialog";
import { SearchGuildsDialog } from "@/components/guild/SearchGuildsDialog";
import { BottomNav } from "@/components/BottomNav";

export function NoGuildView() {
    return (
        <>
            <div
                style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
                className="fixed inset-0 bg-left bg-cover filter blur-sm scale-105"
            />
            <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70 flex flex-col items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-white mb-2">You are not in a guild</h1>
                    <p className="text-white/80 mb-6">Join a guild to compete with others or create your own!</p>
                    <div className="flex justify-center gap-4">
                        <SearchGuildsDialog />
                        <CreateGuildDialog />
                    </div>
                </div>
                <BottomNav />
            </div>
        </>
    );
}
