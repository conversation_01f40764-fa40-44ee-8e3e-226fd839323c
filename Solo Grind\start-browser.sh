#!/bin/bash

echo "Starting SoloGrind Development Server and Opening Browser..."
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    echo ""
fi

# Start the development server in background
echo "Starting development server..."
npm run dev &
SERVER_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 5

# Open browser
echo "Opening browser..."
if command -v xdg-open > /dev/null; then
    xdg-open http://localhost:8080
elif command -v open > /dev/null; then
    open http://localhost:8080
else
    echo "Please open http://localhost:8080 in your browser"
fi

echo ""
echo "Development server is running at http://localhost:8080"
echo "Press Ctrl+C to stop the server"
echo ""

# Wait for the server process
wait $SERVER_PID
