// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xutzuilymxmhcqhnohwq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1dHp1aWx5bXhtaGNxaG5vaHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjkwODAsImV4cCI6MjA2NTUwNTA4MH0.ooqEbQrmVwq7uEPXVkzh11g2_AfgbrEI0EHR2fnxZo4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);