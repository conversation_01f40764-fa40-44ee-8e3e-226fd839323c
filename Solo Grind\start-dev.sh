#!/bin/bash

echo "Starting SoloGrind in Development Mode..."
echo ""
echo "Environment: Development"
echo "Port: 8080"
echo "URL: http://localhost:8080"
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    echo ""
fi

# Start the development server
echo "Starting development server..."
npm run dev

# Open browser automatically (optional)
# sleep 3
# if command -v xdg-open > /dev/null; then
#     xdg-open http://localhost:8080
# elif command -v open > /dev/null; then
#     open http://localhost:8080
# fi
