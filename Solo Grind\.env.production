# Production Environment Configuration
VITE_APP_ENV=production
VITE_APP_NAME=SoloGrind
VITE_APP_VERSION=1.0.0

# Supabase Configuration
VITE_SUPABASE_URL=https://xutzuilymxmhcqhnohwq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1dHp1aWx5bXhtaGNxaG5vaHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjkwODAsImV4cCI6MjA2NTUwNTA4MH0.ooqEbQrmVwq7uEPXVkzh11g2_AfgbrEI0EHR2fnxZo4

# Production Features
VITE_ENABLE_SUBSCRIPTIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PAYMENTS=true
VITE_DEBUG_MODE=false

# Analytics Configuration (to be configured for production)
VITE_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
VITE_MIXPANEL_TOKEN=your_mixpanel_token_here

# Payment Configuration (to be configured for production)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key_here

# Production Optimizations
VITE_BUILD_SOURCEMAP=false
VITE_MINIFY=true
