
import { useEffect, useState, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { BottomNav } from '@/components/BottomNav';
import { Loader2, Send } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tables } from '@/integrations/supabase/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';

type MessageWithProfile = Tables<'messages'> & {
  profiles: Pick<Tables<'profiles'>, 'username' | 'avatar_url'> | null;
};

const fetchMessages = async () => {
  const { data, error } = await supabase
    .from('messages')
    .select('*, profiles(username, avatar_url)')
    .order('created_at', { ascending: true })
    .limit(100);

  if (error) {
    console.error('Error fetching messages:', error);
    throw new Error(error.message);
  }
  return data as MessageWithProfile[];
};

function MessageItem({ message, isCurrentUser }: { message: MessageWithProfile; isCurrentUser: boolean }) {
  return (
    <div className={`flex items-start gap-3 ${isCurrentUser ? 'flex-row-reverse' : ''}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={message.profiles?.avatar_url || undefined} />
        <AvatarFallback>{message.profiles?.username?.[0].toUpperCase() || 'U'}</AvatarFallback>
      </Avatar>
      <div className={`flex flex-col ${isCurrentUser ? 'items-end' : 'items-start'}`}>
        <div className={`p-3 rounded-lg max-w-xs md:max-w-md ${isCurrentUser ? 'bg-electric/80' : 'glass-card'}`}>
          <p className="text-sm text-white break-words">{message.content}</p>
        </div>
        <div className="text-2xs text-white/50 mt-1 px-1 flex items-center gap-1">
          {!isCurrentUser && <span className="font-semibold">{message.profiles?.username || 'User'}</span>}
          <span>{formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}</span>
        </div>
      </div>
    </div>
  );
}

function MessageList({ messages, userId }: { messages: MessageWithProfile[], userId: string | null }) {
    const messagesEndRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    return (
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {messages.map((message) => (
                <MessageItem key={message.id} message={message} isCurrentUser={message.user_id === userId} />
            ))}
            <div ref={messagesEndRef} />
        </div>
    );
}


function ChatInput() {
    const [content, setContent] = useState('');
    const { user } = useAuth();

    const mutation = useMutation({
        mutationFn: async (newContent: string) => {
            if (!user) throw new Error('User not logged in');
            const { error } = await supabase.from('messages').insert({
                content: newContent,
                user_id: user.id,
            });
            if (error) {
                console.error("Error sending message:", error);
                throw error;
            }
        },
        onSuccess: () => {
             setContent('');
        },
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (content.trim()) {
            mutation.mutate(content.trim());
        }
    };

    return (
        <form onSubmit={handleSubmit} className="flex items-center p-4 bg-glass border-t border-white/10">
            <Input
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Type a message..."
                className="bg-transparent border-none focus-visible:ring-0 text-white placeholder:text-white/60"
                autoComplete="off"
                disabled={mutation.isPending}
            />
            <Button type="submit" size="icon" className="bg-electric rounded-full h-9 w-9 shrink-0" disabled={mutation.isPending || !content.trim()}>
                {mutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
            </Button>
        </form>
    );
}

export default function ChatPage() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { data: messages, isLoading } = useQuery({
    queryKey: ['messages'],
    queryFn: fetchMessages,
  });

  useEffect(() => {
    const channel = supabase
      .channel('public:messages')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'messages' },
        (_payload) => {
           queryClient.invalidateQueries({ queryKey: ['messages'] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  return (
    <div className="pb-16 pt-6 min-h-screen bg-backdrop flex flex-col">
      <div className="px-4 pb-2 border-b border-white/10">
        <h1 className="gradient-title text-xl text-center font-bold">Global Chat</h1>
      </div>
      
      <div className="flex-1 flex flex-col overflow-y-hidden">
        {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-electric animate-spin" />
            </div>
        ) : (
            <MessageList messages={messages || []} userId={user?.id || null} />
        )}
        
        <ChatInput />
      </div>

      <BottomNav />
    </div>
  );
}
