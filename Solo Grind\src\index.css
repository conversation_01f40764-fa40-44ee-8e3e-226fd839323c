
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 195 100% 50%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 195 100% 50%;
    --radius: 0.5rem;
  }
}

body {
  @apply bg-backdrop text-white font-inter;
}

.glass-card {
  @apply backdrop-blur-xl shadow-glass bg-glass rounded-xl border border-white/10;
  transition: background 0.2s, box-shadow 0.2s;
}

.stat-card {
  @apply glass-card px-4 py-3 flex flex-col items-center;
}

.stat-label {
  @apply text-xs uppercase tracking-wider text-electric font-semibold;
}

.stat-value {
  @apply text-2xl font-bold text-white leading-tight;
}

.gradient-title {
  background: linear-gradient(90deg, #00d4ff 30%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 900;
}

.tier-badge {
  @apply px-2 py-0.5 rounded-lg font-bold text-xs shadow-glow;
  background: linear-gradient(90deg, #00d4ff 10%, #8b5cf6 120%);
  color: #0a0a0a;
  border: 1.5px solid #00d4ff;
  animation: glow 2s infinite alternate;
}

.avatar-glow {
  box-shadow: 0 0 0 4px #00d4ff44, 0 0 32px 8px #8b5cf648;
  border-radius: 9999px;
  overflow: hidden;
}

::-webkit-scrollbar {
  height: 6px;
  background: #181c22;
}
::-webkit-scrollbar-thumb {
  background: #22282f;
  border-radius: 3px;
}

/* For fade/slide/glass/animated elements, add more as needed */
