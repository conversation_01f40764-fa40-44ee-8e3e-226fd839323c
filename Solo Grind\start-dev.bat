@echo off
echo Starting SoloGrind in Development Mode...
echo.
echo Environment: Development
echo Port: 8080
echo URL: http://localhost:8080
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

REM Start the development server
echo Starting development server...
npm run dev

REM Open browser automatically (optional)
REM timeout /t 3 /nobreak > nul
REM start http://localhost:8080

pause
