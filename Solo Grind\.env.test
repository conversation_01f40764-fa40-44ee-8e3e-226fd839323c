# Test Environment Configuration
VITE_APP_ENV=test
VITE_APP_NAME=SoloGrind-Test
VITE_APP_VERSION=1.0.0

# Supabase Configuration (using same as dev for now)
VITE_SUPABASE_URL=https://xutzuilymxmhcqhnohwq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1dHp1aWx5bXhtaGNxaG5vaHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjkwODAsImV4cCI6MjA2NTUwNTA4MH0.ooqEbQrmVwq7uEPXVkzh11g2_AfgbrEI0EHR2fnxZo4

# Test Features
VITE_ENABLE_SUBSCRIPTIONS=false
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_PAYMENTS=false
VITE_DEBUG_MODE=true

# Test Configuration
VITE_TEST_MODE=true
VITE_MOCK_PAYMENTS=true
VITE_MOCK_ANALYTICS=true

# Analytics Configuration (disabled in test)
VITE_GOOGLE_ANALYTICS_ID=
VITE_MIXPANEL_TOKEN=

# Payment Configuration (disabled in test)
VITE_STRIPE_PUBLISHABLE_KEY=
