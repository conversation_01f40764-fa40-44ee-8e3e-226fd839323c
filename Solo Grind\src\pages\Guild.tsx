import { BottomNav } from "@/components/BottomNav";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useGuild } from "@/hooks/useGuild";
import { GuildLoading } from "@/components/guild/GuildLoading";
import { NoGuildView } from "@/components/guild/NoGuildView";
import { GuildView } from "@/components/guild/GuildView";

export default function Guild() {
    const { loading: authLoading } = useAuth();
    const {
        guild,
        members,
        userGuild,
        isLoadingUserGuild,
        isLoadingMembers,
        userGuildError,
        membersError,
        leaveGuild,
    } = useGuild();

    if (isLoadingUserGuild || authLoading) {
        return <GuildLoading />;
    }
    
    if (userGuildError || membersError) {
        return (
            <>
                <div
                  style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
                  className="fixed inset-0 bg-left bg-cover filter blur-sm scale-105"
                />
                <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70 flex items-center justify-center">
                    <Alert variant="destructive" className="mx-4">
                        <Terminal className="h-4 w-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>
                            {userGuildError?.message || membersError?.message}
                        </AlertDescription>
                    </Alert>
                    <BottomNav />
                </div>
            </>
        )
    }

    if (!userGuild) {
        return <NoGuildView />;
    }
    
    if (!guild) {
        return (
            <>
                <div
                  style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
                  className="fixed inset-0 bg-left bg-cover filter blur-sm scale-105"
                />
                <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70 flex flex-col items-center justify-center text-center">
                    <Alert variant="destructive" className="mx-4">
                        <Terminal className="h-4 w-4" />
                        <AlertTitle>Guild Data Missing</AlertTitle>
                        <AlertDescription>
                            You appear to be in a guild, but we couldn't load its details. This might be a temporary issue or data inconsistency.
                        </AlertDescription>
                    </Alert>
                    <Button
                        onClick={() => leaveGuild.mutate()}
                        disabled={leaveGuild.isPending}
                        variant="destructive"
                        className="mt-4"
                    >
                        {leaveGuild.isPending ? 'Leaving...' : 'Leave Guild'}
                    </Button>
                    <BottomNav />
                </div>
            </>
        );
    }
    
    return (
        <>
            <div
              style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
              className="fixed inset-0 bg-left bg-cover filter blur-sm scale-105"
            />
            <div className="relative z-10 bg-transparent">
                <GuildView 
                    guild={guild} 
                    members={members} 
                    isLoadingMembers={isLoadingMembers}
                    leaveGuildMutation={leaveGuild}
                />
            </div>
        </>
    );
}
