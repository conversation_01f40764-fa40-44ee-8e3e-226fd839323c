@echo off
echo Starting SoloGrind Development Server and Opening Browser...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

REM Start the development server in background
echo Starting development server...
start /B npm run dev

REM Wait for server to start
echo Waiting for server to start...
timeout /t 5 /nobreak > nul

REM Open browser
echo Opening browser...
start http://localhost:8080

echo.
echo Development server is running at http://localhost:8080
echo Press Ctrl+C to stop the server
echo.

REM Keep the window open
pause
