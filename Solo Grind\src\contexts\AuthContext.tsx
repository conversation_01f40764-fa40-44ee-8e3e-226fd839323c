import { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { useQuery } from '@tanstack/react-query';
import { Database } from '@/integrations/supabase/types';
import { isSubscriptionsEnabled, debugLog } from '@/lib/config';

type Profile = Database['public']['Tables']['profiles']['Row'];
type UserRole = Database['public']['Enums']['app_role'];

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  role: UserRole | null;
  loading: boolean;
  signOut: () => Promise<void>;
  isSubscribed: boolean;
  isTrialActive: boolean;
  trialEndDate: Date | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubscribed, setIsSubscribed] = useState(false); // Placeholder for Stripe status
  const [trialEndDate, setTrialEndDate] = useState<Date | null>(null);
  const [isTrialActive, setIsTrialActive] = useState(false);

  useEffect(() => {
    const setAuthData = (session: Session | null) => {
        setSession(session);
        const currentUser = session?.user ?? null;
        setUser(currentUser);

        if (currentUser?.created_at) {
          const createdAt = new Date(currentUser.created_at);
          // 7-day trial period
          const endDate = new Date(createdAt.getTime() + 7 * 24 * 60 * 60 * 1000);
          setTrialEndDate(endDate);
          setIsTrialActive(new Date() < endDate);
        } else {
          setTrialEndDate(null);
          setIsTrialActive(false);
        }
    }

    supabase.auth.getSession().then(({ data: { session } }) => {
      setAuthData(session);
      setLoading(false);
    });

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setAuthData(session);
      setLoading(false);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const { data: profile } = useQuery({
    queryKey: ['profile', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  const { data: role } = useQuery({
    queryKey: ['role', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', user.id)
        .single();
      if (error) {
        console.error('Error fetching role:', error);
        return null;
      }
      return data?.role as UserRole;
    },
    enabled: !!user,
  });

  // Environment-aware subscription check
  useEffect(() => {
      if (user && isSubscriptionsEnabled()) {
          // In a real scenario, you would call a Supabase Edge Function here
          // to check the user's subscription status with Stripe.
          debugLog("Checking subscription status for user:", user.id);
          // TODO: Implement actual Stripe subscription check
      } else if (user && !isSubscriptionsEnabled()) {
          debugLog("Subscriptions disabled in current environment");
          // In development, treat all users as having active trial
          setIsSubscribed(false);
      }
  }, [user]);

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const value = {
    session,
    user,
    profile: profile ?? null,
    role: role ?? null,
    loading: loading || (!!user && (profile === undefined || role === undefined)),
    signOut,
    isSubscribed,
    isTrialActive,
    trialEndDate,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
