// Environment configuration utility
export const config = {
  // App configuration
  app: {
    name: import.meta.env.VITE_APP_NAME || 'SoloGrind',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment: import.meta.env.VITE_APP_ENV || 'development',
  },

  // Feature flags
  features: {
    subscriptions: import.meta.env.VITE_ENABLE_SUBSCRIPTIONS === 'true',
    analytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
    payments: import.meta.env.VITE_ENABLE_PAYMENTS === 'true',
    debugMode: import.meta.env.VITE_DEBUG_MODE === 'true',
    testMode: import.meta.env.VITE_TEST_MODE === 'true',
  },

  // Supabase configuration
  supabase: {
    url: import.meta.env.VITE_SUPABASE_URL || '',
    anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
  },

  // Analytics configuration
  analytics: {
    googleAnalyticsId: import.meta.env.VITE_GOOGLE_ANALYTICS_ID || '',
    mixpanelToken: import.meta.env.VITE_MIXPANEL_TOKEN || '',
  },

  // Payment configuration
  payments: {
    stripePublishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
  },

  // Development configuration
  dev: {
    serverPort: import.meta.env.VITE_DEV_SERVER_PORT || 8080,
    serverHost: import.meta.env.VITE_DEV_SERVER_HOST || 'localhost',
  },
};

// Helper functions
export const isDevelopment = () => config.app.environment === 'development';
export const isProduction = () => config.app.environment === 'production';
export const isTest = () => config.app.environment === 'test';

// Feature checks
export const isSubscriptionsEnabled = () => config.features.subscriptions;
export const isAnalyticsEnabled = () => config.features.analytics;
export const isPaymentsEnabled = () => config.features.payments;
export const isDebugMode = () => config.features.debugMode;

// Debug logging utility
export const debugLog = (...args: any[]) => {
  if (isDebugMode()) {
    console.log('[DEBUG]', ...args);
  }
};

// Environment info for debugging
export const getEnvironmentInfo = () => ({
  environment: config.app.environment,
  features: config.features,
  isDev: isDevelopment(),
  isProd: isProduction(),
  isTest: isTest(),
});

// Log environment info on load (only in debug mode)
if (isDebugMode()) {
  console.log('Environment Configuration:', getEnvironmentInfo());
}
