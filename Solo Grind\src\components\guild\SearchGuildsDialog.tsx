
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Tables } from '@/integrations/supabase/types';
import { DynamicIcon } from '@/components/DynamicIcon';
import { Skeleton } from '../ui/skeleton';

type Guild = Tables<'guilds'>;

const searchGuilds = async (searchTerm: string): Promise<Guild[]> => {
  if (!searchTerm.trim()) return [];
  const { data, error } = await supabase
    .from('guilds')
    .select('*')
    .ilike('name', `%${searchTerm}%`)
    .limit(10);
  if (error) throw new Error(error.message);
  return data || [];
};

export function SearchGuildsDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const timer = setTimeout(() => setDebouncedSearchTerm(searchTerm), 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  const { data: guilds, isLoading } = useQuery({
    queryKey: ['searchGuilds', debouncedSearchTerm],
    queryFn: () => searchGuilds(debouncedSearchTerm),
    enabled: debouncedSearchTerm.length > 0,
  });

  const joinGuildMutation = useMutation({
    mutationFn: async (guildId: string) => {
      if (!user) throw new Error('You must be logged in.');

      const { data: existingMember, error: checkError } = await supabase
        .from('guild_members')
        .select('guild_id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (checkError) throw new Error(`Failed to check guild status: ${checkError.message}`);
      if (existingMember) throw new Error('You are already in a guild.');
      
      const { error: insertError } = await supabase
        .from('guild_members')
        .insert({ guild_id: guildId, user_id: user.id, role: 'member' });

      if (insertError) throw new Error(insertError.message);
    },
    onSuccess: () => {
      toast({
        title: 'Joined Guild!',
        description: "Welcome! You're now a member.",
      });
      queryClient.invalidateQueries({ queryKey: ['userGuild', user?.id] });
      setIsOpen(false);
      setSearchTerm('');
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Error Joining Guild',
        description: error.message,
      });
    },
  });

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>Search Guilds</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Find a Guild</DialogTitle>
        </DialogHeader>
        <Input
          placeholder="Guild name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <div className="mt-4 space-y-2 h-64 overflow-y-auto pr-2">
          {isLoading && (
            <div className="space-y-2">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
            </div>
          )}
          {!isLoading && guilds && guilds.length > 0 && guilds.map((guild) => (
            <div key={guild.id} className="flex items-center justify-between p-2 rounded-lg border border-border bg-background/50">
              <div className="flex items-center gap-3">
                <DynamicIcon name={guild.icon_name || 'Shield'} className="h-8 w-8 text-primary" />
                <span className="font-semibold">{guild.name}</span>
              </div>
              <Button
                size="sm"
                onClick={() => joinGuildMutation.mutate(guild.id)}
                disabled={joinGuildMutation.isPending}
              >
                {joinGuildMutation.isPending ? 'Joining...' : 'Join'}
              </Button>
            </div>
          ))}
          {!isLoading && guilds?.length === 0 && debouncedSearchTerm && (
            <p className="text-center text-muted-foreground pt-8">No guilds found.</p>
          )}
           {!isLoading && !debouncedSearchTerm && (
            <p className="text-center text-muted-foreground pt-8">Start typing to search for a guild.</p>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

