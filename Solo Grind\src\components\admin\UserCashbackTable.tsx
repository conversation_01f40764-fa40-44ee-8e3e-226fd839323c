
import React from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { GlassCard } from "../GlassCard";

interface CashbackData {
  username: string;
  cashback_percentage: number;
  payout_amount: number;
  payout_month: number;
  payout_year: number;
}

interface Props {
  data: CashbackData[];
}

export function UserCashbackTable({ data }: Props) {
  const getMonthName = (monthNumber: number) => {
    const date = new Date();
    date.setMonth(monthNumber - 1);
    return date.toLocaleString('en-US', { month: 'long' });
  }

  return (
    <GlassCard className="p-4">
      <h2 className="text-xl mb-4 text-white/90">Recent Cashback Payouts</h2>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-white/80">Username</TableHead>
              <TableHead className="text-white/80">Payout Period</TableHead>
              <TableHead className="text-right text-white/80">Cashback %</TableHead>
              <TableHead className="text-right text-white/80">Payout Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center text-white/70 h-24">
                  No cashback has been paid out yet.
                </TableCell>
              </TableRow>
            ) : (
              data.map((payout) => (
                <TableRow key={payout.username + payout.payout_year + payout.payout_month}>
                  <TableCell className="font-medium text-white/90">{payout.username}</TableCell>
                  <TableCell className="text-white/90">{`${getMonthName(payout.payout_month)} ${payout.payout_year}`}</TableCell>
                  <TableCell className="text-right text-electric font-mono">{payout.cashback_percentage.toFixed(2)}%</TableCell>
                  <TableCell className="text-right text-electric font-mono">${payout.payout_amount.toFixed(2)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </GlassCard>
  );
}
