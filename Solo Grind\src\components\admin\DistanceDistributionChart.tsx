
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { GlassCard } from "../GlassCard";

interface ChartData {
  name: string;
  count: number;
}

interface Props {
  data: ChartData[];
}

const chartConfig = {
  count: {
    label: "Activities",
    color: "hsl(var(--chart-1))",
  },
};

export function DistanceDistributionChart({ data }: Props) {
  return (
    <GlassCard className="p-4">
      <h2 className="text-xl mb-4 text-white/90">Activity Distance Distribution</h2>
      {data.every((d) => d.count === 0) ? (
        <p className="text-center text-white/70 h-[250px] flex items-center justify-center">
          No activity data to display in chart.
        </p>
      ) : (
        <ChartContainer config={chartConfig} className="h-[250px] w-full">
          <Bar<PERSON><PERSON> data={data} accessibilityLayer>
            <CartesianGrid vertical={false} strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tick={{ fill: 'rgba(255, 255, 255, 0.7)' }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tick={{ fill: 'rgba(255, 255, 255, 0.7)' }}
              allowDecimals={false}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dot" />}
            />
            <Bar dataKey="count" fill="var(--color-count)" radius={4} />
          </BarChart>
        </ChartContainer>
      )}
    </GlassCard>
  );
}
