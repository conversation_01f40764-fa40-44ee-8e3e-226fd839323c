import { BottomNav } from "@/components/BottomNav";
import { GlassCard } from "@/components/GlassCard";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { Check, Info } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

const features = {
  free: ["5 activities per week", "Basic stats", "Community access"],
  pro: [
    "Unlimited activities",
    "Advanced analytics",
    "Premium support",
    "Exclusive badges",
    "Withdraw cashback",
    "Up to 90% cashback on 5km+ walks",
    "50% cashback on 1km+ walks",
  ],
};

export default function Subscription() {
  const { isTrialActive, trialEndDate, isSubscribed, role } = useAuth();
  const { toast } = useToast();

  const handleUpgrade = () => {
    // This is where we would trigger the Stripe checkout flow.
    // For now, it just logs to the console and shows a toast.
    console.log("Upgrading to Pro...");
    toast({
      title: "Coming Soon!",
      description: "Stripe payment integration is the next step.",
    });
  };
  
  const handleManageSubscription = () => {
    toast({
      title: "Coming Soon!",
      description: "Subscription management is the next step.",
    });
  }

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-top bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4">
          <h1 className="gradient-title text-2xl mb-2 text-center">
            {isSubscribed ? "Your Plan" : "Upgrade Your Plan"}
          </h1>
          <p className="text-center text-white/70 mb-8">
            {isSubscribed 
              ? "You have full access to all features." 
              : "Unlock exclusive features and support the app!"}
          </p>

          {!isSubscribed && isTrialActive && trialEndDate && (
            <GlassCard className="p-4 mb-6 max-w-md mx-auto text-center border border-electric/50">
              <div className="flex items-center justify-center gap-2">
                <Info className="text-electric size-5" />
                <p className="text-white">
                  Your free trial ends on {trialEndDate.toLocaleDateString()}.
                </p>
              </div>
            </GlassCard>
          )}

          {!isSubscribed && !isTrialActive && role !== 'admin' && (
            <GlassCard className="p-4 mb-6 max-w-md mx-auto text-center border border-yellow-500/50">
              <div className="flex items-center justify-center gap-2">
                <Info className="text-yellow-500 size-5" />
                <p className="text-white">
                  Your free trial has ended. Please subscribe to continue.
                </p>
              </div>
            </GlassCard>
          )}
          
          <div className="space-y-6 max-w-md mx-auto">
              {/* Free Plan Card */}
              <GlassCard className={`p-6 border ${isTrialActive && !isSubscribed ? 'border-electric' : 'border-white/10'}`}>
                <h2 className="text-2xl font-bold text-white">Free Trial</h2>
                <p className="text-white/70 mt-1">For your first week</p>
                <p className="text-4xl font-bold my-4 text-white">₹0<span className="text-base font-normal text-white/70">/7 days</span></p>
                <Button variant="outline" className="w-full" disabled>
                    {isTrialActive && !isSubscribed ? "Your Current Plan" : "Trial Plan"}
                </Button>
                <ul className="mt-6 space-y-2">
                    {features.free.map(feature => (
                        <li key={feature} className="flex items-center gap-2 text-white/80">
                            <Check className="text-electric size-4" />
                            <span>{feature}</span>
                        </li>
                    ))}
                </ul>
              </GlassCard>

              {/* Pro Plan */}
              <GlassCard className={`p-6 border relative overflow-hidden ${isSubscribed ? 'border-electric' : 'border-white/10'}`}>
                {isSubscribed && <div className="absolute top-0 right-0 bg-electric text-backdrop text-xs font-bold px-4 py-1 rounded-bl-lg">YOUR PLAN</div>}
                {!isSubscribed && <div className="absolute top-0 right-0 bg-electric text-backdrop text-xs font-bold px-4 py-1 rounded-bl-lg">BEST VALUE</div>}
                <h2 className="text-2xl font-bold text-electric">Pro</h2>
                <p className="text-white/70 mt-1">For dedicated athletes</p>
                <p className="text-4xl font-bold my-4 text-electric">₹1000<span className="text-base font-normal text-white/70">/month</span></p>
                
                {isSubscribed ? (
                    <Button variant="outline" className="w-full" onClick={handleManageSubscription}>Manage Subscription</Button>
                ) : (
                    <Button className="w-full bg-electric text-backdrop hover:bg-electric/90" onClick={handleUpgrade}>Upgrade to Pro</Button>
                )}
                 <ul className="mt-6 space-y-2">
                    {features.pro.map(feature => (
                        <li key={feature} className="flex items-center gap-2 text-white/80">
                            <Check className="text-electric size-4" />
                            <span>{feature}</span>
                        </li>
                    ))}
                </ul>
              </GlassCard>
          </div>
        </div>
        <BottomNav />
      </div>
    </>
  );
}
