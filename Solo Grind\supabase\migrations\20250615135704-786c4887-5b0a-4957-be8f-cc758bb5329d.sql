
-- Add location columns to the guilds table for location-based recommendations
ALTER TABLE public.guilds ADD COLUMN IF NOT EXISTS latitude DOUBLE PRECISION;
ALTER TABLE public.guilds ADD COLUMN IF NOT EXISTS longitude DOUBLE PRECISION;

-- <PERSON><PERSON> Policies for Guild Members
DROP POLICY IF EXISTS "Allow authenticated users to join guilds" ON public.guild_members;
CREATE POLICY "Allow authenticated users to join guilds"
ON public.guild_members
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Allow authenticated users to leave guilds" ON public.guild_members;
CREATE POLICY "Allow authenticated users to leave guilds"
ON public.guild_members
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- RLS Policy for Guild Creation
DROP POLICY IF EXISTS "Allow authenticated users to create guilds" ON public.guilds;
CREATE POLICY "Allow authenticated users to create guilds"
ON public.guilds
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Function to create a new guild and assign the creator as leader
CREATE OR REPLACE FUNCTION public.create_guild(
  p_name TEXT,
  p_icon_name TEXT,
  p_latitude DOUBLE PRECISION,
  p_longitude DOUBLE PRECISION
)
RETURNS uuid
LANGUAGE plpgsql
AS $$
DECLARE
  new_guild_id uuid;
  creator_id uuid := auth.uid();
BEGIN
  -- Insert the new guild and get its ID
  INSERT INTO public.guilds (name, icon_name, latitude, longitude)
  VALUES (p_name, p_icon_name, p_latitude, p_longitude)
  RETURNING id INTO new_guild_id;

  -- Add the creator as a member with the 'leader' role
  INSERT INTO public.guild_members (guild_id, user_id, role)
  VALUES (new_guild_id, creator_id, 'leader');

  RETURN new_guild_id;
END;
$$;
