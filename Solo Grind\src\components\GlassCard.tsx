
import React from "react";

interface GlassCardProps {
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export function GlassCard({
  className = "",
  children,
  onClick,
  style,
}: GlassCardProps) {
  return (
    <div
      className={`glass-card ${className} ${onClick ? "cursor-pointer hover:bg-glass/80" : ""} transition-all duration-150`}
      onClick={onClick}
      style={style}
    >
      {children}
    </div>
  );
}
