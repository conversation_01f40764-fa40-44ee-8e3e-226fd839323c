import * as React from "react";
import { GlassCard } from "@/components/GlassCard";
import { TierBadge } from "@/components/TierBadge";
import { BottomNav } from "@/components/BottomNav";
import { Trophy, CalendarClock, Flame } from "lucide-react";

const TIERS = [
  { t: "D", km: 1 },
  { t: "C", km: 2 },
  { t: "B", km: 3 },
  { t: "A", km: 4 },
  { t: "S", km: 5 },
];

type TierAchievement = {
  type: "tier";
  title: string;
  icon: React.ReactNode;
  unlocked: boolean;
  tier: "D" | "C" | "B" | "A" | "S";
};

type MiscAchievement = {
  type: "misc";
  title: string;
  icon: React.ReactNode;
  unlocked: boolean;
};

type Achievement = TierAchievement | MiscAchievement;

const iconProps = {
  size: 36,
  className: "text-electric",
};

const allAchievements: Achievement[] = [
  ...TIERS.map(
    tier =>
      ({
        type: "tier",
        title: `Daily ${tier.km}km Run`,
        icon: <Trophy {...iconProps} />,
        unlocked: tier.t !== "A", // Just demo logic
        tier: tier.t as "D" | "C" | "B" | "A" | "S",
      } as TierAchievement)
  ),
  {
    type: "misc",
    title: "Weekly 25km",
    icon: <CalendarClock {...iconProps} />,
    unlocked: false,
  },
  {
    type: "misc",
    title: "Streak: 7 days",
    icon: <Flame {...iconProps} />,
    unlocked: true,
  },
];

export default function Achievements() {
  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-right bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 mb-2">
          <div className="flex items-center gap-3 mb-3">
            <h2 className="gradient-title text-xl">Achievements</h2>
          </div>
          <div className="grid grid-cols-3 gap-3">
            {allAchievements.map((a, i) => (
              <GlassCard
                key={a.title}
                className={`flex flex-col items-center justify-center py-4 min-h-[120px] transition-all shadow-glow
                  ${!a.unlocked ? "opacity-60 grayscale-[0.8] border border-purple/30" : ""}`}
              >
                <div className="w-9 h-9 mb-1 flex items-center justify-center">
                  {a.icon}
                </div>
                <span className="font-bold text-xs mb-1 text-center">{a.title}</span>
                {a.type === "tier" && <TierBadge tier={a.tier} />}
                {!a.unlocked && <span className="text-xs text-purple mt-2">Locked</span>}
              </GlassCard>
            ))}
          </div>
        </div>
        <BottomNav />
      </div>
    </>
  );
}
