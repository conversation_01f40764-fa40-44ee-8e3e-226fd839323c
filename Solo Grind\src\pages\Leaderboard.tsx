
import * as React from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Database } from "@/integrations/supabase/types";
import { TierBadge } from "@/components/TierBadge";
import { GlassCard } from "@/components/GlassCard";
import { BottomNav } from "@/components/BottomNav";
import { DynamicIcon } from "@/components/DynamicIcon";
import { Loader2 } from "lucide-react";

type User = Database['public']['Views']['user_leaderboard_stats']['Row'] & { tier: "S" | "A" | "B" | "C" | "D" };
type Guild = Database['public']['Tables']['guilds']['Row'] & { members: number };

const fetchOverallLeaderboard = async () => {
  const { data, error } = await supabase
    .from('user_leaderboard_stats')
    .select('*')
    .order('total_distance', { ascending: false })
    .limit(100);
  if (error) throw error;
  return data.map(u => ({ ...u, tier: 'B' as const, id: u.user_id }));
};

const fetchGuildLeaderboard = async () => {
    const { data, error } = await supabase
        .from('guilds')
        .select(`
            *,
            guild_members(count)
        `)
        .order('xp', { ascending: false })
        .limit(50);
    if (error) throw error;
    return data.map(g => ({ ...g, members: g.guild_members[0]?.count || 0 }));
};

const fetchMyGuildLeaderboard = async (userId: string | undefined) => {
    if (!userId) return [];
    
    const { data: memberData, error: memberError } = await supabase
        .from('guild_members')
        .select('guild_id')
        .eq('user_id', userId)
        .maybeSingle();
    
    if (memberError || !memberData) return [];

    const { guild_id } = memberData;

    const { data: memberIds, error: memberIdsError } = await supabase
        .from('guild_members')
        .select('user_id')
        .eq('guild_id', guild_id);
    
    if (memberIdsError) throw memberIdsError;

    const userIds = memberIds.map(m => m.user_id);

    const { data: leaderboardData, error: leaderboardError } = await supabase
        .from('user_leaderboard_stats')
        .select('*')
        .in('user_id', userIds)
        .order('total_distance', { ascending: false });

    if (leaderboardError) throw leaderboardError;
    return leaderboardData.map(u => ({ ...u, tier: 'B' as const, id: u.user_id }));
};

const tabs = ["Overall", "My Guild", "Guilds"];

function LeaderRow({ user, idx, currentUserId }: { user: User, idx: number, currentUserId: string | null }) {
  const isCurrentUser = user.user_id === currentUserId;
  let special = "";
  if (idx === 0) special = "border-2 border-yellow-400 shadow-[0_0_12px_2px_#ffe066]";
  if (idx === 1) special = "border-2 border-gray-200 shadow-[0_0_8px_2px_#fafafa99]";
  if (idx === 2) special = "border-2 border-yellow-700 shadow-[0_0_8px_2px_#eab30899]";

  return (
    <GlassCard
      className={`flex items-center gap-3 my-1 py-2 px-2 ${isCurrentUser ? "ring-2 ring-electric" : ""} ${special}`}
      style={isCurrentUser ? { background: "rgba(0,212,255,0.13)", filter: "drop-shadow(0 0 4px #00d4ff88)" } : {}}
    >
      <span className={`font-bold text-xl w-6 text-center ${idx < 3 ? "gradient-title" : ""}`}>{idx + 1}</span>
      <img src={user.avatar_url || undefined} alt={user.username || 'User'} className="w-9 h-9 rounded-full border-2 border-white/20" />
      <div className="flex-1 min-w-0">
        <div className="truncate font-semibold">{user.username} {isCurrentUser && <span className="text-xs ml-1 text-electric">(You)</span>}</div>
        <div className="flex items-center gap-1">
          <span className="text-white/60 text-xs">{(user.total_distance || 0).toFixed(1)} km</span>
          <TierBadge tier={user.tier} />
        </div>
      </div>
    </GlassCard>
  );
}

function GuildRow({ guild, idx }: { guild: Guild, idx: number }) {
  let special = "";
  if (idx === 0) special = "border-2 border-yellow-400 shadow-[0_0_12px_2px_#ffe066]";
  if (idx === 1) special = "border-2 border-gray-200 shadow-[0_0_8px_2px_#fafafa99]";
  if (idx === 2) special = "border-2 border-yellow-700 shadow-[0_0_8px_2px_#eab30899]";
  const iconProps = { size: 24, className: "text-electric" };

  return (
    <GlassCard
      className={`flex items-center gap-3 my-1 py-2 px-2 ${special}`}
    >
      <span className={`font-bold text-xl w-6 text-center ${idx < 3 ? "gradient-title" : ""}`}>{idx + 1}</span>
      <div className="w-9 h-9 rounded-md border-2 border-white/20 flex items-center justify-center bg-glass/20">
        {guild.icon_name && <DynamicIcon name={guild.icon_name} {...iconProps} />}
      </div>
      <div className="flex-1 min-w-0">
        <div className="truncate font-semibold">{guild.name}</div>
        <div className="flex items-center gap-1">
          <span className="text-white/60 text-xs">{guild.members} members</span>
        </div>
      </div>
      <div className="text-electric font-bold">{guild.xp.toLocaleString()} XP</div>
    </GlassCard>
  );
}

export default function Leaderboard() {
  const [tab, setTab] = React.useState(tabs[0]);
  const { user: authUser } = useAuth();

  const { data: overallData, isLoading: isLoadingOverall } = useQuery({
    queryKey: ['leaderboard', 'overall'],
    queryFn: fetchOverallLeaderboard,
    enabled: tab === 'Overall',
  });

  const { data: myGuildData, isLoading: isLoadingMyGuild } = useQuery({
    queryKey: ['leaderboard', 'my-guild', authUser?.id],
    queryFn: () => fetchMyGuildLeaderboard(authUser?.id),
    enabled: tab === 'My Guild' && !!authUser,
  });

  const { data: guildsData, isLoading: isLoadingGuilds } = useQuery({
    queryKey: ['leaderboard', 'guilds'],
    queryFn: fetchGuildLeaderboard,
    enabled: tab === 'Guilds',
  });

  const isLoading = isLoadingOverall || isLoadingMyGuild || isLoadingGuilds;
  
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <Loader2 className="w-8 h-8 text-electric animate-spin" />
        </div>
      );
    }
    
    if (tab === 'Overall' && overallData) {
      return overallData.map((user, i) => (
        <LeaderRow user={user} idx={i} key={user.user_id} currentUserId={authUser?.id || null} />
      ));
    }
    
    if (tab === 'My Guild') {
      if (!myGuildData || myGuildData.length === 0) {
        return <p className="text-center text-white/70 mt-8">You are not in a guild, or your guild has no members.</p>
      }
      return myGuildData.map((user, i) => (
        <LeaderRow user={user} idx={i} key={user.user_id} currentUserId={authUser?.id || null} />
      ));
    }

    if (tab === 'Guilds' && guildsData) {
      return guildsData.map((guild, i) => (
        <GuildRow guild={guild} idx={i} key={guild.id} />
      ));
    }

    return <p className="text-center text-white/70 mt-8">No data available.</p>;
  }

  return (
    <div className="pb-20 pt-6 min-h-screen bg-backdrop flex flex-col">
      <div className="flex gap-2 mx-4 mb-4">
        {tabs.map(name => (
          <button
            key={name}
            onClick={() => setTab(name)}
            className={`glass-card px-3 py-1 flex-1 font-bold text-sm uppercase transition-all
              ${tab === name ? "bg-gradient-to-r from-electric to-purple text-backdrop shadow-glow" : "text-white/70"}`}
          >
            {name}
          </button>
        ))}
      </div>

      <div className="flex-1 flex flex-col gap-2 px-4 overflow-y-auto">
        {renderContent()}
      </div>

      <BottomNav />
    </div>
  );
}
