
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";
import { GlassCard } from "@/components/GlassCard";
import { BottomNav } from "@/components/BottomNav";
import { WalletSummary } from "@/components/wallet/WalletSummary";
import { TransactionHistory } from "@/components/wallet/TransactionHistory";

export default function Wallet() {
  const { user } = useAuth();

  const { data: wallet, isLoading: isWalletLoading, error: walletError } = useQuery({
    queryKey: ["wallet", user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from("wallets")
        .select("*")
        .eq("user_id", user.id)
        .maybeSingle();
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!user,
  });

  const { data: transactions, isLoading: isTransactionsLoading, error: transactionsError } = useQuery({
    queryKey: ["wallet-transactions", wallet?.id],
    queryFn: async () => {
      if (!wallet) return [];
      const { data, error } = await supabase
        .from("wallet_transactions")
        .select("*")
        .eq("wallet_id", wallet.id)
        .order("created_at", { ascending: false });
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!wallet,
  });

  const isLoading = isWalletLoading || isTransactionsLoading;
  const error = walletError || transactionsError;

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-left-top bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4">
          <h1 className="gradient-title text-2xl mb-4">My Wallet</h1>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 text-electric animate-spin" />
            </div>
          ) : error ? (
            <GlassCard className="p-4 bg-red-500/20 border-red-500/50">
              <p className="text-white">Error loading data: {(error as Error).message}</p>
            </GlassCard>
          ) : wallet ? (
            <div className="space-y-4">
              <WalletSummary wallet={wallet} />
              <TransactionHistory transactions={transactions || []} />
            </div>
          ) : (
             <GlassCard className="p-4 text-center">
              <p className="text-white/80">No wallet found. A wallet will be created for you automatically.</p>
            </GlassCard>
          )}
        </div>
        <BottomNav />
      </div>
    </>
  );
}
